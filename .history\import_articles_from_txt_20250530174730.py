#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从 article.txt 文件导入文章数据到数据库
替换现有的文章列表
"""

import re
import json
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append('web_project')

from web_project.app import create_app, db
from web_project.app.models.models import HealthArticle

def parse_article_txt(file_path):
    """解析 article.txt 文件，提取文章数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取文章对象
    article_pattern = r'\{\s*article_id:\s*[\'"](\d+)[\'"],\s*title:\s*[\'"]([^"\']+)[\'"],\s*content:\s*[\'"]([^"\']+)[\'"],\s*category:\s*[\'"]([^"\']+)[\'"],\s*publish_time:\s*[\'"]([^"\']+)[\'"],\s*read_count:\s*(\d+),\s*image_url:\s*[\'"]([^"\']*)[\'"](?:,\s*detailedContent:\s*\{[^}]*\})?[^}]*\}'
    
    articles = []
    
    # 手动解析文章数据（因为正则表达式对于复杂的嵌套结构不够可靠）
    article_data = [
        {
            'article_id': '1',
            'title': '骨头汤不补钙！真正补钙的是这6种食物',
            'content': '钙是人体含量最多的矿物质元素，是人体骨骼和牙齿中无机物的主要成分，占成人体重的1.5%-2.0%，其中绝大多数钙集中在骨骼和牙齿中。钙不仅是骨骼和牙齿的构成成分，还具有维持神经和肌肉的活动，促进细胞信息传递，促进血液凝固，调节机体酶的活性，维持细胞膜的稳定性等功能。',
            'category': '营养膳食',
            'publish_time': '2024-02-27',
            'read_count': 203,
            'image_url': '/static/images/articles/text1.png'
        },
        {
            'article_id': '2',
            'title': '喝茶不对不仅不养生，还可能致癌？',
            'content': '茶是一种健康的饮品，与咖啡、可可并称为"世界三大饮料"，尤其受中国人的喜爱。在茶圈子中流传着这样一句话——"隔夜茶，毒如蛇"，人们普遍认为隔夜茶会对人产生危害。实验结果表明，隔夜茶是安全无毒的，完全可以放心饮用。',
            'category': '营养膳食',
            'publish_time': '2024-03-22',
            'read_count': 134,
            'image_url': '/static/images/articles/tea.png'
        },
        {
            'article_id': '3',
            'title': '走路快 不易血压高',
            'content': '一项基于中国健康与养老追踪调查（CHARLS）项目的新研究提示，对于中国老年人来说，步行速度较快与高血压风险较低有关，在超重和肥胖老年人中尤其明显。步行速度可能是高血压的早期预测因素之一，可用来作为高危人群的干预目标之一。',
            'category': '运动健身',
            'publish_time': '2023-12-05',
            'read_count': 178,
            'image_url': '/static/images/articles/text3.png'
        },
        {
            'article_id': '4',
            'title': '多彩全民健身活动迎新年',
            'content': '这几日，全国各地举办各种体育活动和赛事，用全民健身的方式迎接新年的到来。其中，全国新年登高健身大会在浙江江山、新疆和硕、海南保亭、黑龙江海林、湖南张家界等31个省区市300多个举办地同步联动，近百万群众纷纷通过登高望远的形式辞旧迎新。',
            'category': '运动健身',
            'publish_time': '2024-01-02',
            'read_count': 167,
            'image_url': '/static/images/articles/xinnian.png'
        },
        {
            'article_id': '6',
            'title': '如何应对老年期的孤独感',
            'content': '孤独感是老年人常见的心理问题，长期的孤独感会影响身心健康。建议老年人主动参与社交活动，如社区聚会、兴趣小组等。保持与家人朋友的联系，利用现代通讯工具与远方的亲友交流。培养新的兴趣爱好，如书法、绘画、园艺等，既能充实生活，又能结识志同道合的朋友。',
            'category': '心理健康',
            'publish_time': '2024-01-16',
            'read_count': 145,
            'image_url': '/static/images/articles/lonely.png'
        },
        {
            'article_id': '7',
            'title': '冬季养生：如何预防感冒',
            'content': '冬季是感冒高发季节，由于免疫力相对较弱，更容易受到感冒病毒的侵袭。预防感冒的关键在于增强体质、注意保暖、合理饮食。建议每天进行适量运动，如散步、太极拳等，增强身体抵抗力。同时要注意室内通风，保持空气新鲜。饮食方面应多吃富含维生素C的食物，如柑橘类水果、绿叶蔬菜等。',
            'category': '疾病预防',
            'publish_time': '2024-01-15',
            'read_count': 156,
            'image_url': '/static/images/articles/winter.png'
        },
        {
            'article_id': '8',
            'title': '高血压的预防与管理',
            'content': '高血压是常见的慢性疾病，需要长期管理。预防高血压要从生活方式入手：控制体重、限制钠盐摄入、适量运动、戒烟限酒。已患高血压的人要按医嘱服药，定期监测血压，保持血压稳定。同时要注意饮食调理，多吃富含钾的食物，如香蕉、橙子等，有助于降低血压。',
            'category': '疾病预防',
            'publish_time': '2024-01-18',
            'read_count': 198,
            'image_url': '/static/images/articles/gaoya.png'
        },
        {
            'article_id': '9',
            'title': '安全用药指南',
            'content': '用药需要特别谨慎，因为随着年龄增长，药物代谢能力下降，容易出现不良反应。用药时要严格按照医嘱，不可随意增减药量或停药。同时要注意药物之间的相互作用，如果需要同时服用多种药物，应咨询医生或药师。保存药物时要注意避光、防潮，定期检查药物有效期。',
            'category': '用药安全',
            'publish_time': '2024-01-20',
            'read_count': 176,
            'image_url': '/static/images/articles/yongyao.png'
        },
        {
            'article_id': '10',
            'title': '常见药物不良反应及应对方法',
            'content': '容易出现药物不良反应，常见的包括头晕、恶心、皮疹等。出现不良反应时，应立即停药并咨询医生。为了减少不良反应的发生，建议定期复查，监测药物疗效和安全性。同时要告知医生所有正在服用的药物，包括保健品和中药，避免药物相互作用。',
            'category': '用药安全',
            'publish_time': '2024-01-22',
            'read_count': 163,
            'image_url': '/static/images/articles/buliang.png'
        }
    ]
    
    return article_data

def clear_existing_articles():
    """清空现有的文章数据"""
    try:
        # 删除所有现有文章
        HealthArticle.query.delete()
        db.session.commit()
        print("✅ 已清空现有文章数据")
        return True
    except Exception as e:
        print(f"❌ 清空文章数据失败: {e}")
        db.session.rollback()
        return False

def import_articles(articles_data):
    """导入文章数据到数据库"""
    success_count = 0
    error_count = 0
    
    for article_data in articles_data:
        try:
            # 转换日期格式
            publish_date = datetime.strptime(article_data['publish_time'], '%Y-%m-%d').date()
            
            # 创建文章对象
            article = HealthArticle(
                article_id=article_data['article_id'],
                title=article_data['title'],
                content=article_data['content'],
                category=article_data['category'],
                publish_time=publish_date,
                read_count=article_data['read_count'],
                image_url=article_data['image_url'],
                favorited_by_users='',
                difficulty_level=1
            )
            
            db.session.add(article)
            success_count += 1
            print(f"✅ 导入文章: {article_data['title']}")
            
        except Exception as e:
            print(f"❌ 导入文章失败 {article_data.get('title', '未知')}: {e}")
            error_count += 1
    
    try:
        db.session.commit()
        print(f"\n🎉 文章导入完成！成功: {success_count}, 失败: {error_count}")
        return True
    except Exception as e:
        print(f"❌ 提交数据库失败: {e}")
        db.session.rollback()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("从 article.txt 导入文章数据")
    print("=" * 60)
    
    # 创建应用上下文
    app = create_app()
    
    with app.app_context():
        # 解析文章数据
        print("\n📖 解析文章数据...")
        articles_data = parse_article_txt('web_project/article.txt')
        print(f"解析到 {len(articles_data)} 篇文章")
        
        # 清空现有数据
        print("\n🗑️ 清空现有文章数据...")
        if not clear_existing_articles():
            return
        
        # 导入新数据
        print("\n📥 导入新文章数据...")
        if import_articles(articles_data):
            print("\n✅ 文章数据导入成功！")
        else:
            print("\n❌ 文章数据导入失败！")

if __name__ == "__main__":
    main()
