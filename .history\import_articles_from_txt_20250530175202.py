#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从 article.txt 文件导入文章数据到数据库
替换现有的文章列表
"""

import re
import json
from datetime import datetime
import sys
import os
import requests

# 使用API方式导入，避免数据库连接冲突
BASE_URL = "http://127.0.0.1:5000"

def parse_article_txt(file_path):
    """解析 article.txt 文件，提取文章数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 使用正则表达式提取文章对象
    article_pattern = r'\{\s*article_id:\s*[\'"](\d+)[\'"],\s*title:\s*[\'"]([^"\']+)[\'"],\s*content:\s*[\'"]([^"\']+)[\'"],\s*category:\s*[\'"]([^"\']+)[\'"],\s*publish_time:\s*[\'"]([^"\']+)[\'"],\s*read_count:\s*(\d+),\s*image_url:\s*[\'"]([^"\']*)[\'"](?:,\s*detailedContent:\s*\{[^}]*\})?[^}]*\}'

    articles = []

    # 手动解析文章数据（因为正则表达式对于复杂的嵌套结构不够可靠）
    article_data = [
        {
            'article_id': '1',
            'title': '骨头汤不补钙！真正补钙的是这6种食物',
            'content': '钙是人体含量最多的矿物质元素，是人体骨骼和牙齿中无机物的主要成分，占成人体重的1.5%-2.0%，其中绝大多数钙集中在骨骼和牙齿中。钙不仅是骨骼和牙齿的构成成分，还具有维持神经和肌肉的活动，促进细胞信息传递，促进血液凝固，调节机体酶的活性，维持细胞膜的稳定性等功能。',
            'category': '营养膳食',
            'publish_time': '2024-02-27',
            'read_count': 203,
            'image_url': '/static/images/articles/text1.png'
        },
        {
            'article_id': '2',
            'title': '喝茶不对不仅不养生，还可能致癌？',
            'content': '茶是一种健康的饮品，与咖啡、可可并称为"世界三大饮料"，尤其受中国人的喜爱。在茶圈子中流传着这样一句话——"隔夜茶，毒如蛇"，人们普遍认为隔夜茶会对人产生危害。实验结果表明，隔夜茶是安全无毒的，完全可以放心饮用。',
            'category': '营养膳食',
            'publish_time': '2024-03-22',
            'read_count': 134,
            'image_url': '/static/images/articles/tea.png'
        },
        {
            'article_id': '3',
            'title': '走路快 不易血压高',
            'content': '一项基于中国健康与养老追踪调查（CHARLS）项目的新研究提示，对于中国老年人来说，步行速度较快与高血压风险较低有关，在超重和肥胖老年人中尤其明显。步行速度可能是高血压的早期预测因素之一，可用来作为高危人群的干预目标之一。',
            'category': '运动健身',
            'publish_time': '2023-12-05',
            'read_count': 178,
            'image_url': '/static/images/articles/text3.png'
        },
        {
            'article_id': '4',
            'title': '多彩全民健身活动迎新年',
            'content': '这几日，全国各地举办各种体育活动和赛事，用全民健身的方式迎接新年的到来。其中，全国新年登高健身大会在浙江江山、新疆和硕、海南保亭、黑龙江海林、湖南张家界等31个省区市300多个举办地同步联动，近百万群众纷纷通过登高望远的形式辞旧迎新。',
            'category': '运动健身',
            'publish_time': '2024-01-02',
            'read_count': 167,
            'image_url': '/static/images/articles/xinnian.png'
        },
        {
            'article_id': '6',
            'title': '如何应对老年期的孤独感',
            'content': '孤独感是老年人常见的心理问题，长期的孤独感会影响身心健康。建议老年人主动参与社交活动，如社区聚会、兴趣小组等。保持与家人朋友的联系，利用现代通讯工具与远方的亲友交流。培养新的兴趣爱好，如书法、绘画、园艺等，既能充实生活，又能结识志同道合的朋友。',
            'category': '心理健康',
            'publish_time': '2024-01-16',
            'read_count': 145,
            'image_url': '/static/images/articles/lonely.png'
        },
        {
            'article_id': '7',
            'title': '冬季养生：如何预防感冒',
            'content': '冬季是感冒高发季节，由于免疫力相对较弱，更容易受到感冒病毒的侵袭。预防感冒的关键在于增强体质、注意保暖、合理饮食。建议每天进行适量运动，如散步、太极拳等，增强身体抵抗力。同时要注意室内通风，保持空气新鲜。饮食方面应多吃富含维生素C的食物，如柑橘类水果、绿叶蔬菜等。',
            'category': '疾病预防',
            'publish_time': '2024-01-15',
            'read_count': 156,
            'image_url': '/static/images/articles/winter.png'
        },
        {
            'article_id': '8',
            'title': '高血压的预防与管理',
            'content': '高血压是常见的慢性疾病，需要长期管理。预防高血压要从生活方式入手：控制体重、限制钠盐摄入、适量运动、戒烟限酒。已患高血压的人要按医嘱服药，定期监测血压，保持血压稳定。同时要注意饮食调理，多吃富含钾的食物，如香蕉、橙子等，有助于降低血压。',
            'category': '疾病预防',
            'publish_time': '2024-01-18',
            'read_count': 198,
            'image_url': '/static/images/articles/gaoya.png'
        },
        {
            'article_id': '9',
            'title': '安全用药指南',
            'content': '用药需要特别谨慎，因为随着年龄增长，药物代谢能力下降，容易出现不良反应。用药时要严格按照医嘱，不可随意增减药量或停药。同时要注意药物之间的相互作用，如果需要同时服用多种药物，应咨询医生或药师。保存药物时要注意避光、防潮，定期检查药物有效期。',
            'category': '用药安全',
            'publish_time': '2024-01-20',
            'read_count': 176,
            'image_url': '/static/images/articles/yongyao.png'
        },
        {
            'article_id': '10',
            'title': '常见药物不良反应及应对方法',
            'content': '容易出现药物不良反应，常见的包括头晕、恶心、皮疹等。出现不良反应时，应立即停药并咨询医生。为了减少不良反应的发生，建议定期复查，监测药物疗效和安全性。同时要告知医生所有正在服用的药物，包括保健品和中药，避免药物相互作用。',
            'category': '用药安全',
            'publish_time': '2024-01-22',
            'read_count': 163,
            'image_url': '/static/images/articles/buliang.png'
        },
        {
            'article_id': '13',
            'title': '只吃粗粮真的会更健康吗？看看你吃对了没',
            'content': '随着饮食水平和养生意识的提高，越来越多的朋友开始注重膳食平衡。然而，有一些朋友干脆把所有主食都用粗粮替代，认为这样不仅可以减肥，还更健康。粗粮确实含有丰富的膳食纤维、维生素B族、矿物质等营养成分，但完全用粗粮替代精制谷物并不科学。',
            'category': '营养膳食',
            'publish_time': '2024-03-22',
            'read_count': 165,
            'image_url': '/static/images/articles/culiang.png'
        },
        {
            'article_id': '14',
            'title': '节日期间合理安排饮食 保持食物清淡原则',
            'content': '节日期间，家庭聚餐增多，如何合理安排饮食，保持身体健康？专家建议保持食物清淡原则，避免暴饮暴食。节日期间，人们往往会摄入过多高脂肪、高热量、高盐分的食物，这些食物虽然美味，但容易导致消化不良、血糖血脂升高等问题。',
            'category': '营养膳食',
            'publish_time': '2024-01-02',
            'read_count': 143,
            'image_url': '/static/images/articles/holiday.png'
        },
        {
            'article_id': '15',
            'title': '重感冒多休息 剧烈运动易"伤心"',
            'content': '前不久，来自重庆的一名知名主持人因病去世，年仅42岁。据悉，他是患重感冒后未痊愈，在晚上大运动量锻炼时，诱发了心肌梗死。感冒期间，人体免疫系统正在与病毒作斗争，身体处于相对虚弱的状态。此时进行剧烈运动，会进一步消耗体力，降低免疫力。',
            'category': '运动健身',
            'publish_time': '2023-12-05',
            'read_count': 156,
            'image_url': '/static/images/articles/ganmao.png'
        },
        {
            'article_id': '16',
            'title': '如何预防运动损伤',
            'content': '运动时容易发生损伤，预防措施包括：运动前充分热身，运动后适当放松；选择合适的运动强度，避免过度疲劳；穿着合适的运动鞋，选择安全的运动场所；如有不适立即停止运动。常见的运动损伤有肌肉拉伤、关节扭伤等，一旦发生应及时处理，必要时就医。',
            'category': '运动健身',
            'publish_time': '2024-02-08',
            'read_count': 134,
            'image_url': '/static/images/articles/run.png'
        },
        {
            'article_id': '17',
            'title': '如何保持积极心态',
            'content': '积极的心态对身心健康至关重要。建议培养乐观的生活态度，学会感恩和知足；保持学习的热情，可以学习新技能或培养新爱好；多与他人交流，分享生活感悟；适当参与志愿服务，体现自身价值。遇到困难时，要学会寻求帮助，不要独自承受压力。',
            'category': '心理健康',
            'publish_time': '2024-02-10',
            'read_count': 128,
            'image_url': '/static/images/articles/xintai.png'
        },
        {
            'article_id': '18',
            'title': '老年人心理健康的重要性',
            'content': '心理健康对老年人的整体健康状况有重要影响。保持积极乐观的心态，多与家人朋友交流，参加社区活动，培养兴趣爱好，都有助于维护心理健康。如果出现持续的情绪低落、焦虑等症状，应及时寻求专业帮助。良好的心理状态不仅能提高生活质量，还能增强免疫力，延缓衰老。',
            'category': '心理健康',
            'publish_time': '2024-01-05',
            'read_count': 134,
            'image_url': '/static/images/articles/xinli.png'
        },
        {
            'article_id': '19',
            'title': '糖尿病的预防与管理',
            'content': '糖尿病是常见的慢性疾病，预防措施包括：控制体重，保持健康的BMI；合理饮食，限制糖分和精制碳水化合物的摄入；规律运动，每周至少150分钟中等强度运动；定期体检，监测血糖水平。已患糖尿病的人要按医嘱用药，监测血糖，预防并发症的发生。',
            'category': '疾病预防',
            'publish_time': '2024-02-15',
            'read_count': 189,
            'image_url': '/static/images/articles/tangniao.png'
        },
        {
            'article_id': '20',
            'title': '冬季气温下降，如何科学应对脑卒中',
            'content': '随着冬季气温降低，卒中的风险也在增加。近一段时间，记者在北京、吉林等地的医院发现，因卒中前来就诊的患者明显增加。脑卒中患者以老年人居多，且往往都有一些脑卒中的高危因素，比如高血压、糖尿病、高脂血症或者冠心病等，天气一旦出现变化，血压骤然增加，就可以造成血管破裂。',
            'category': '疾病预防',
            'publish_time': '2024-02-18',
            'read_count': 234,
            'image_url': '/static/images/articles/brain.png'
        },
        {
            'article_id': '21',
            'title': '多重用药的注意事项',
            'content': '往往患有多种疾病，需要同时服用多种药物，这增加了用药风险。多重用药的注意事项包括：定期复查，评估药物的必要性；了解药物间的相互作用；按时按量服药，不可随意增减；使用药盒分装，避免漏服或重复服用。建议建立用药档案，记录所有正在服用的药物。',
            'category': '用药安全',
            'publish_time': '2024-02-20',
            'read_count': 145,
            'image_url': '/static/images/articles/duocong.png'
        },
        {
            'article_id': '22',
            'title': '中药使用安全指南',
            'content': '中药在人群中使用广泛，但也需要注意安全性。使用中药的注意事项包括：选择正规渠道购买中药；遵医嘱使用，不可自行调整剂量；注意中药与西药的相互作用；观察用药后的反应，如有不适及时停药就医。一些中药可能含有重金属或有毒成分，长期使用需要定期检查肝肾功能。',
            'category': '用药安全',
            'publish_time': '2024-02-22',
            'read_count': 158,
            'image_url': '/static/images/articles/zhongyao.png'
        },
        {
            'article_id': '23',
            'title': '常见急症的家庭急救',
            'content': '容易发生心脏病、中风、跌倒等急症。家属应掌握基本的急救知识：心脏病发作时让患者保持安静，服用硝酸甘油；疑似中风时立即拨打120，记录发病时间；跌倒后不要急于搀扶，先检查是否有骨折。平时要准备急救药品，熟悉急救电话，关键时刻能够正确应对。',
            'category': '急救常识',
            'publish_time': '2024-01-25',
            'read_count': 187,
            'image_url': '/static/images/articles/jijiu.png'
        },
        {
            'article_id': '24',
            'title': 'CPR心肺复苏术基础知识',
            'content': 'CPR是挽救生命的重要技能，每个人都应该掌握。CPR的操作要点包括：判断意识和呼吸、拨打急救电话、进行胸外按压和人工呼吸。按压位置在胸骨下半部，深度5-6厘米，频率100-120次/分钟。虽然骨骼较脆弱，但在生命危急时刻，正确的CPR仍然是最有效的急救措施。',
            'category': '急救常识',
            'publish_time': '2024-01-28',
            'read_count': 142,
            'image_url': '/static/images/articles/CPR.png'
        },
        {
            'article_id': '25',
            'title': '突发疾病的识别与处理',
            'content': '突发疾病时，正确的识别和处理至关重要。常见的突发疾病包括：心绞痛、心肌梗死、脑卒中、低血糖等。识别要点：胸痛、呼吸困难可能是心脏病；突然说话不清、肢体无力可能是脑卒中；出汗、心慌、饥饿感可能是低血糖。处理原则：保持冷静、立即呼救、采取相应的急救措施。',
            'category': '急救常识',
            'publish_time': '2024-02-25',
            'read_count': 176,
            'image_url': '/static/images/articles/tufa.png'
        },
        {
            'article_id': '26',
            'title': '家庭急救包的准备',
            'content': '家庭急救包是家庭必备的安全保障。急救包应包含：常用急救药品（硝酸甘油、速效救心丸、降压药等）；医疗器械（血压计、体温计、血糖仪等）；外伤处理用品（创可贴、纱布、碘伏等）；紧急联系卡（家属电话、医生电话、急救电话）。定期检查急救包，确保药品在有效期内，器械功能正常。',
            'category': '急救常识',
            'publish_time': '2024-02-28',
            'read_count': 163,
            'image_url': '/static/images/articles/bag.png'
        },
        {
            'article_id': '27',
            'title': '抑郁症，凶手竟是甲状腺',
            'content': '刘奶奶今年67岁了，退休前是一名光荣的纺织女工。两个月前体检发现甲状腺结节，手术切除后却出现了情绪低落、乏力等症状。原来，甲状腺功能减退和抑郁症之间存在密不可分的联系。甲状腺分泌的甲状腺素能够增加神经系统的兴奋性，当甲状腺功能减退时，神经系统的兴奋性下降，从而使患者出现情绪的低落与心情的变化。',
            'category': '心理健康',
            'publish_time': '2022-07-06',
            'read_count': 234,
            'image_url': '/static/images/articles/yiyu.png'
        }
    ]

    return article_data

def login_as_worker():
    """以W_B02身份登录"""
    login_data = {
        "user_type": "worker",
        "user_id": "W_B02",
        "password": "123456"
    }

    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ W_B02登录成功")
            return response.cookies
        else:
            print(f"❌ W_B02登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def get_existing_articles(cookies):
    """获取现有文章列表"""
    try:
        response = requests.get(f"{BASE_URL}/api/health_articles/manage/articles", cookies=cookies)
        if response.status_code == 200:
            data = response.json()
            return data.get('articles', [])
        else:
            print(f"❌ 获取文章列表失败: {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取文章列表请求失败: {e}")
        return []

def delete_article(article_id, cookies):
    """删除单篇文章"""
    try:
        response = requests.delete(f"{BASE_URL}/api/health_articles/manage/articles/{article_id}", cookies=cookies)
        if response.status_code == 200:
            return True
        else:
            print(f"❌ 删除文章 {article_id} 失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 删除文章 {article_id} 请求失败: {e}")
        return False

def clear_existing_articles(cookies):
    """清空现有的文章数据"""
    print("🗑️ 获取现有文章列表...")
    existing_articles = get_existing_articles(cookies)

    if not existing_articles:
        print("✅ 没有现有文章需要删除")
        return True

    print(f"📋 找到 {len(existing_articles)} 篇现有文章，开始删除...")
    success_count = 0

    for article in existing_articles:
        article_id = article.get('article_id')
        if article_id and delete_article(article_id, cookies):
            success_count += 1
            print(f"✅ 删除文章: {article.get('title', article_id)}")
        else:
            print(f"❌ 删除文章失败: {article.get('title', article_id)}")

    print(f"🗑️ 删除完成，成功删除 {success_count}/{len(existing_articles)} 篇文章")
    return success_count == len(existing_articles)

def create_article(article_data, cookies):
    """创建单篇文章"""
    try:
        response = requests.post(f"{BASE_URL}/api/health_articles/manage/articles", json=article_data, cookies=cookies)
        if response.status_code == 201:
            return True
        else:
            print(f"❌ 创建文章失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 创建文章请求失败: {e}")
        return False

def import_articles(articles_data, cookies):
    """导入文章数据"""
    success_count = 0
    error_count = 0

    for article_data in articles_data:
        if create_article(article_data, cookies):
            success_count += 1
            print(f"✅ 导入文章: {article_data['title']}")
        else:
            error_count += 1
            print(f"❌ 导入文章失败: {article_data.get('title', '未知')}")

    print(f"\n🎉 文章导入完成！成功: {success_count}, 失败: {error_count}")
    return success_count > 0

def main():
    """主函数"""
    print("=" * 60)
    print("从 article.txt 导入文章数据")
    print("=" * 60)

    # 登录获取权限
    print("\n🔐 登录W_B02账户...")
    cookies = login_as_worker()
    if not cookies:
        print("❌ 登录失败，无法继续")
        return

    # 解析文章数据
    print("\n📖 解析文章数据...")
    articles_data = parse_article_txt('web_project/article.txt')
    print(f"解析到 {len(articles_data)} 篇文章")

    # 清空现有数据
    print("\n🗑️ 清空现有文章数据...")
    if not clear_existing_articles(cookies):
        print("⚠️ 清空现有数据时出现问题，但继续导入新数据...")

    # 导入新数据
    print("\n📥 导入新文章数据...")
    if import_articles(articles_data, cookies):
        print("\n✅ 文章数据导入成功！")
    else:
        print("\n❌ 文章数据导入失败！")

if __name__ == "__main__":
    main()
