#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康科普文章API测试脚本
测试W_B02社区人员发布的文章是否能在E01老人端正常显示
"""

import requests
import json

# 服务器地址
BASE_URL = "http://127.0.0.1:5000"

def test_get_articles():
    """测试获取文章列表API"""
    print("=" * 50)
    print("测试获取文章列表API")
    print("=" * 50)
    
    url = f"{BASE_URL}/api/health_articles/articles"
    params = {
        'page': 1,
        'per_page': 10
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if 'articles' in data:
                print(f"成功获取 {len(data['articles'])} 篇文章")
                for i, article in enumerate(data['articles'][:3]):  # 只显示前3篇
                    print(f"文章 {i+1}: {article.get('title', '无标题')}")
            else:
                print("响应中没有找到articles字段")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_get_favorites():
    """测试获取收藏文章API"""
    print("\n" + "=" * 50)
    print("测试获取收藏文章API")
    print("=" * 50)
    
    user_id = "E01"
    url = f"{BASE_URL}/api/health_articles/articles/user/{user_id}/favorites"
    
    try:
        response = requests.get(url)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if 'favorites' in data:
                print(f"用户 {user_id} 收藏了 {len(data['favorites'])} 篇文章")
                for i, article in enumerate(data['favorites']):
                    print(f"收藏文章 {i+1}: {article.get('title', '无标题')}")
            else:
                print("响应中没有找到favorites字段")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_toggle_favorite():
    """测试切换收藏状态API"""
    print("\n" + "=" * 50)
    print("测试切换收藏状态API")
    print("=" * 50)
    
    article_id = "A001"  # 使用数据库中存在的文章ID
    user_id = "E01"
    url = f"{BASE_URL}/api/health_articles/articles/{article_id}/favorite"
    
    data = {
        'user_id': user_id
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"请求URL: {response.url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"返回数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if 'action' in result:
                action = result['action']
                if action == 'favorited':
                    print(f"✅ 成功收藏文章 {article_id}")
                elif action == 'unfavorited':
                    print(f"✅ 成功取消收藏文章 {article_id}")
            else:
                print("响应中没有找到action字段")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    print("开始测试健康科普文章API...")
    
    # 测试获取文章列表
    test_get_articles()
    
    # 测试获取收藏文章
    test_get_favorites()
    
    # 测试切换收藏状态
    test_toggle_favorite()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)
