#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证文章导入结果
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_article_import():
    """测试文章导入结果"""
    print("=" * 60)
    print("验证文章导入结果")
    print("=" * 60)
    
    # 1. 测试获取文章列表
    print("\n1. 测试获取文章列表...")
    response = requests.get(f"{BASE_URL}/api/health_articles/articles")
    
    if response.status_code == 200:
        data = response.json()
        articles = data.get('articles', [])
        total = data.get('total', 0)
        
        print(f"✅ 获取文章列表成功")
        print(f"📊 文章总数: {total}")
        print(f"📋 当前页文章数: {len(articles)}")
        
        # 按分类统计
        categories = {}
        for article in articles:
            category = article.get('category', '未分类')
            categories[category] = categories.get(category, 0) + 1
        
        print(f"\n📈 分类统计:")
        for category, count in categories.items():
            print(f"  - {category}: {count}篇")
        
        # 显示前几篇文章
        print(f"\n📝 前5篇文章:")
        for i, article in enumerate(articles[:5]):
            print(f"  {i+1}. {article.get('title', '无标题')} ({article.get('category', '未分类')})")
    
    else:
        print(f"❌ 获取文章列表失败: {response.status_code}")
        print(f"错误信息: {response.text}")
    
    # 2. 测试获取分类列表
    print(f"\n2. 测试获取分类列表...")
    response = requests.get(f"{BASE_URL}/api/health_articles/articles/categories")
    
    if response.status_code == 200:
        data = response.json()
        categories = data.get('categories', [])
        print(f"✅ 获取分类列表成功")
        print(f"📋 分类列表: {categories}")
    else:
        print(f"❌ 获取分类列表失败: {response.status_code}")
    
    # 3. 测试按分类筛选
    print(f"\n3. 测试按分类筛选...")
    test_category = "营养膳食"
    response = requests.get(f"{BASE_URL}/api/health_articles/articles?category={test_category}")
    
    if response.status_code == 200:
        data = response.json()
        articles = data.get('articles', [])
        print(f"✅ 按分类筛选成功")
        print(f"📊 '{test_category}'分类文章数: {len(articles)}")
        for article in articles:
            print(f"  - {article.get('title', '无标题')}")
    else:
        print(f"❌ 按分类筛选失败: {response.status_code}")

if __name__ == "__main__":
    test_article_import()
