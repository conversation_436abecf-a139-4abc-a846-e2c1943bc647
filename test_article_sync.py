#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试W_B02发布文章后E01老人端的同步显示
"""

import requests
import json
import time

# 服务器地址
BASE_URL = "http://127.0.0.1:5000"

def test_create_article():
    """测试W_B02创建新文章"""
    print("=" * 60)
    print("测试W_B02社区工作人员发布新文章")
    print("=" * 60)
    
    # 模拟W_B02登录状态（实际应用中需要先登录获取session）
    url = f"{BASE_URL}/api/health_articles/manage/articles"
    
    # 新文章数据
    article_data = {
        "title": "春季养生小贴士",
        "content": "春季是万物复苏的季节，也是养生的好时机。以下是一些春季养生的小贴士：\n\n1. 早睡早起：春季应该顺应自然规律，早睡早起，保证充足的睡眠。\n\n2. 适度运动：春季气候宜人，是户外运动的好时机。可以选择散步、慢跑、太极拳等运动。\n\n3. 饮食调理：春季应多吃新鲜蔬菜和水果，少吃油腻和辛辣食物。\n\n4. 情志调养：保持心情愉快，避免情绪波动过大。\n\n5. 预防疾病：春季是感冒和过敏的高发季节，要注意预防。",
        "category": "养生",
        "image_url": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400",
        "difficulty_level": 1
    }
    
    try:
        # 发送创建文章请求
        response = requests.post(url, json=article_data)
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(article_data, ensure_ascii=False, indent=2)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ 文章创建成功!")
            print(f"文章ID: {result.get('article_id')}")
            print(f"文章标题: {result.get('article', {}).get('title')}")
            return result.get('article_id')
        else:
            print(f"❌ 文章创建失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_get_articles_after_creation():
    """测试创建文章后获取文章列表"""
    print("\n" + "=" * 60)
    print("测试创建文章后的文章列表")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/articles"
    params = {
        'page': 1,
        'per_page': 5  # 只获取前5篇文章
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            articles = data.get('articles', [])
            print(f"✅ 成功获取 {len(articles)} 篇文章")
            print(f"总文章数: {data.get('total')}")
            
            print("\n最新的5篇文章:")
            for i, article in enumerate(articles):
                print(f"{i+1}. {article.get('title')} (ID: {article.get('article_id')})")
                print(f"   分类: {article.get('category')}, 发布时间: {article.get('publish_time')}")
                
        else:
            print(f"❌ 获取文章列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_update_article(article_id):
    """测试更新文章"""
    if not article_id:
        print("❌ 没有文章ID，跳过更新测试")
        return
        
    print("\n" + "=" * 60)
    print(f"测试更新文章 {article_id}")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/manage/articles/{article_id}"
    
    # 更新数据
    update_data = {
        "title": "春季养生小贴士（已更新）",
        "content": "春季是万物复苏的季节，也是养生的好时机。以下是一些春季养生的小贴士：\n\n1. 早睡早起：春季应该顺应自然规律，早睡早起，保证充足的睡眠。\n\n2. 适度运动：春季气候宜人，是户外运动的好时机。可以选择散步、慢跑、太极拳等运动。\n\n3. 饮食调理：春季应多吃新鲜蔬菜和水果，少吃油腻和辛辣食物。\n\n4. 情志调养：保持心情愉快，避免情绪波动过大。\n\n5. 预防疾病：春季是感冒和过敏的高发季节，要注意预防。\n\n【更新内容】\n6. 春季护肝：春季是养肝的最佳时机，要保持心情舒畅，避免熬夜。",
        "category": "养生"
    }
    
    try:
        response = requests.put(url, json=update_data)
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文章更新成功!")
            print(f"更新后标题: {result.get('article', {}).get('title')}")
        else:
            print(f"❌ 文章更新失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("开始测试W_B02与E01的文章同步功能...")
    
    # 1. 测试创建新文章
    article_id = test_create_article()
    
    # 等待一下，确保数据库操作完成
    time.sleep(1)
    
    # 2. 测试获取文章列表（验证新文章是否出现）
    test_get_articles_after_creation()
    
    # 3. 测试更新文章
    test_update_article(article_id)
    
    # 等待一下
    time.sleep(1)
    
    # 4. 再次获取文章列表（验证更新是否生效）
    print("\n" + "=" * 60)
    print("验证文章更新后的效果")
    print("=" * 60)
    test_get_articles_after_creation()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("现在可以刷新老人端页面查看新发布和更新的文章")
    print("=" * 60)
