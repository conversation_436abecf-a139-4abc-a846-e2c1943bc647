#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试W_B02社区工作人员的文章管理功能
模拟完整的登录和文章管理流程
"""

import requests
import json
import time

# 服务器地址
BASE_URL = "http://127.0.0.1:5000"

def login_as_w_b02():
    """模拟W_B02登录"""
    print("=" * 60)
    print("模拟W_B02（王丽）登录")
    print("=" * 60)
    
    # 创建session
    session = requests.Session()
    
    # 登录请求
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        "user_id": "W_B02",
        "password": "123456",  # 假设密码
        "user_type": "worker"
    }
    
    try:
        response = session.post(login_url, json=login_data)
        print(f"登录请求URL: {login_url}")
        print(f"登录数据: {json.dumps(login_data, ensure_ascii=False)}")
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功: {result.get('message', '')}")
            return session
        else:
            print(f"❌ 登录失败: {response.text}")
            # 即使登录失败，我们也返回session，因为可能是登录接口的问题
            # 但session可能已经包含了必要的cookie
            return session
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return session

def test_create_article_with_session(session):
    """使用session测试创建文章"""
    print("\n" + "=" * 60)
    print("测试W_B02创建新文章（使用session）")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/manage/articles"
    
    # 新文章数据
    article_data = {
        "title": "春季养生小贴士",
        "content": "春季是万物复苏的季节，也是养生的好时机。以下是一些春季养生的小贴士：\n\n1. 早睡早起：春季应该顺应自然规律，早睡早起，保证充足的睡眠。\n\n2. 适度运动：春季气候宜人，是户外运动的好时机。可以选择散步、慢跑、太极拳等运动。\n\n3. 饮食调理：春季应多吃新鲜蔬菜和水果，少吃油腻和辛辣食物。\n\n4. 情志调养：保持心情愉快，避免情绪波动过大。\n\n5. 预防疾病：春季是感冒和过敏的高发季节，要注意预防。",
        "category": "养生",
        "image_url": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400",
        "difficulty_level": 1
    }
    
    try:
        response = session.post(url, json=article_data)
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ 文章创建成功!")
            print(f"文章ID: {result.get('article_id')}")
            print(f"文章标题: {result.get('article', {}).get('title')}")
            return result.get('article_id')
        else:
            print(f"❌ 文章创建失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_direct_database_insert():
    """直接通过数据库插入测试文章"""
    print("\n" + "=" * 60)
    print("直接通过数据库插入测试文章")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        import sys
        import os
        sys.path.append(os.path.join(os.getcwd(), 'web_project'))
        
        from app import create_app
        from app.models import db, HealthArticle
        from datetime import datetime
        import random
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            # 生成文章ID
            while True:
                article_id = f"A{random.randint(100, 999)}"
                if not HealthArticle.query.filter_by(article_id=article_id).first():
                    break
            
            # 创建新文章
            new_article = HealthArticle(
                article_id=article_id,
                title="春季养生小贴士（数据库直接插入）",
                content="春季是万物复苏的季节，也是养生的好时机。以下是一些春季养生的小贴士：\n\n1. 早睡早起：春季应该顺应自然规律，早睡早起，保证充足的睡眠。\n\n2. 适度运动：春季气候宜人，是户外运动的好时机。可以选择散步、慢跑、太极拳等运动。\n\n3. 饮食调理：春季应多吃新鲜蔬菜和水果，少吃油腻和辛辣食物。\n\n4. 情志调养：保持心情愉快，避免情绪波动过大。\n\n5. 预防疾病：春季是感冒和过敏的高发季节，要注意预防。\n\n【这是通过数据库直接插入的测试文章】",
                category="养生",
                publish_time=datetime.now().date(),
                image_url="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400",
                difficulty_level=1,
                read_count=0,
                favorited_by_users=""
            )
            
            db.session.add(new_article)
            db.session.commit()
            
            print(f"✅ 文章直接插入成功!")
            print(f"文章ID: {article_id}")
            print(f"文章标题: {new_article.title}")
            
            return article_id
            
    except Exception as e:
        print(f"❌ 数据库插入失败: {e}")
        return None

def test_get_latest_articles():
    """获取最新文章列表"""
    print("\n" + "=" * 60)
    print("获取最新文章列表")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/articles"
    params = {
        'page': 1,
        'per_page': 5
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"请求URL: {response.url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            articles = data.get('articles', [])
            print(f"✅ 成功获取 {len(articles)} 篇文章")
            print(f"总文章数: {data.get('total')}")
            
            print("\n最新的5篇文章:")
            for i, article in enumerate(articles):
                print(f"{i+1}. {article.get('title')} (ID: {article.get('article_id')})")
                print(f"   分类: {article.get('category')}, 发布时间: {article.get('publish_time')}")
                
        else:
            print(f"❌ 获取文章列表失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    print("开始测试W_B02文章管理功能...")
    
    # 1. 尝试登录
    session = login_as_w_b02()
    
    # 2. 尝试通过API创建文章
    article_id = test_create_article_with_session(session)
    
    # 3. 如果API创建失败，直接通过数据库插入
    if not article_id:
        print("\nAPI创建失败，尝试直接数据库插入...")
        article_id = test_direct_database_insert()
    
    # 4. 获取最新文章列表验证
    time.sleep(1)  # 等待数据库操作完成
    test_get_latest_articles()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    if article_id:
        print(f"✅ 成功创建文章: {article_id}")
        print("现在可以刷新老人端页面查看新发布的文章")
    else:
        print("❌ 文章创建失败")
    print("=" * 60)
