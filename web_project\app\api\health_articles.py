from flask import Blueprint, request, jsonify, session
from app import db
from sqlalchemy import or_
import traceback
import random
from datetime import datetime

# 延迟导入模型以避免循环导入
def get_models():
    try:
        from app.models.models import HealthArticle, ElderlyUser, CommunityWorker
        return HealthArticle, ElderlyUser, CommunityWorker
    except ImportError as e:
        print(f"模型导入错误: {e}")
        return None, None, None

health_articles_bp = Blueprint('health_articles', __name__)

def check_worker_permission():
    """检查当前登录用户是否为有权限的社区工作人员"""
    user_id = session.get('user_id')
    user_type = session.get('user_type')

    # 添加日志输出
    print(f"[DEBUG] check_worker_permission: user_id={user_id}, user_type={user_type}")

    if user_type != 'worker':
        return False, '只有社区工作人员可以管理文章'

    # 检查是否为W_B02（王丽），她负责科普文章管理
    if user_id != 'W_B02':
        return False, '您没有权限管理科普文章'

    return True, None

def generate_article_id():
    """生成文章ID"""
    # 生成格式：A + 3位数字
    while True:
        article_id = f"A{random.randint(100, 999)}"
        HealthArticle, _, _ = get_models()
        if HealthArticle and not HealthArticle.query.filter_by(article_id=article_id).first():
            return article_id

@health_articles_bp.route('/test', methods=['GET'])
def test_api():
    """测试API是否正常工作"""
    return jsonify({'message': '健康科普API正常工作', 'status': 'ok'}), 200

@health_articles_bp.route('/articles', methods=['GET'])
def get_articles():
    """获取健康科普文章列表"""
    try:
        HealthArticle, ElderlyUser, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        # 获取查询参数
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        # 构建查询
        query = HealthArticle.query

        # 按分类筛选
        if category and category != 'all':
            query = query.filter(HealthArticle.category == category)

        # 搜索功能
        if search:
            query = query.filter(
                or_(
                    HealthArticle.title.contains(search),
                    HealthArticle.content.contains(search)
                )
            )

        # 按发布时间倒序排列
        query = query.order_by(HealthArticle.publish_time.desc())

        # 分页
        articles = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 转换为字典格式
        result = {
            'articles': [article.to_dict() for article in articles.items],
            'total': articles.total,
            'pages': articles.pages,
            'current_page': page,
            'per_page': per_page
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"获取文章列表错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'获取文章列表失败: {str(e)}'}), 500

@health_articles_bp.route('/articles/<article_id>', methods=['GET'])
def get_article_detail(article_id):
    """获取文章详情"""
    try:
        HealthArticle, ElderlyUser, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        article = HealthArticle.query.filter_by(article_id=article_id).first()
        if not article:
            return jsonify({'error': '文章不存在'}), 404

        # 增加阅读次数
        article.read_count += 1
        db.session.commit()

        return jsonify(article.to_dict()), 200

    except Exception as e:
        print(f"获取文章详情错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'获取文章详情失败: {str(e)}'}), 500

@health_articles_bp.route('/articles/categories', methods=['GET'])
def get_categories():
    """获取所有文章分类"""
    try:
        HealthArticle, ElderlyUser, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        categories = db.session.query(HealthArticle.category).distinct().all()
        category_list = [cat[0] for cat in categories]

        return jsonify({'categories': category_list}), 200

    except Exception as e:
        print(f"获取分类错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'获取分类失败: {str(e)}'}), 500

@health_articles_bp.route('/articles/<article_id>/favorite', methods=['POST'])
def toggle_favorite(article_id):
    """收藏/取消收藏文章"""
    try:
        HealthArticle, ElderlyUser, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'error': '用户ID不能为空'}), 400

        article = HealthArticle.query.filter_by(article_id=article_id).first()
        if not article:
            return jsonify({'error': '文章不存在'}), 404

        # 获取当前收藏用户列表
        favorited_users = article.favorited_by_users.split(',') if article.favorited_by_users else []

        if user_id in favorited_users:
            # 取消收藏
            favorited_users.remove(user_id)
            action = 'unfavorited'
        else:
            # 添加收藏
            favorited_users.append(user_id)
            action = 'favorited'

        # 更新数据库
        article.favorited_by_users = ','.join(favorited_users) if favorited_users else ''
        db.session.commit()

        return jsonify({
            'action': action,
            'favorited_count': len(favorited_users)
        }), 200

    except Exception as e:
        print(f"收藏操作错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'操作失败: {str(e)}'}), 500

@health_articles_bp.route('/articles/user/<user_id>/favorites', methods=['GET'])
def get_user_favorites(user_id):
    """获取用户收藏的文章"""
    try:
        HealthArticle, ElderlyUser, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        # 查找包含该用户ID的文章
        articles = HealthArticle.query.filter(
            HealthArticle.favorited_by_users.contains(user_id)
        ).order_by(HealthArticle.publish_time.desc()).all()

        result = [article.to_dict() for article in articles]

        return jsonify({'favorites': result}), 200

    except Exception as e:
        print(f"获取收藏列表错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'获取收藏列表失败: {str(e)}'}), 500

# ==================== 文章管理API（仅限W_B02使用） ====================

@health_articles_bp.route('/manage/articles', methods=['POST'])
def create_article():
    """创建新文章（仅限W_B02）"""
    # 检查权限
    has_permission, error_msg = check_worker_permission()
    if not has_permission:
        return jsonify({'error': error_msg}), 403

    try:
        HealthArticle, _, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        data = request.json
        if not data:
            return jsonify({'error': '请提供文章数据'}), 400

        # 验证必填字段
        required_fields = ['title', 'content', 'category']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必填字段: {field}'}), 400

        # 生成文章ID
        article_id = generate_article_id()

        # 创建文章
        article = HealthArticle(
            article_id=article_id,
            title=data['title'],
            content=data['content'],
            category=data['category'],
            publish_time=datetime.now().date(),
            image_url=data.get('image_url', ''),
            difficulty_level=data.get('difficulty_level', 1),
            read_count=0,
            favorited_by_users=''
        )

        db.session.add(article)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文章创建成功',
            'article_id': article_id,
            'article': article.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        print(f"创建文章错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'创建文章失败: {str(e)}'}), 500

@health_articles_bp.route('/manage/articles/<article_id>', methods=['PUT'])
def update_article(article_id):
    """更新文章（仅限W_B02）"""
    # 检查权限
    has_permission, error_msg = check_worker_permission()
    if not has_permission:
        return jsonify({'error': error_msg}), 403

    try:
        HealthArticle, _, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        article = HealthArticle.query.filter_by(article_id=article_id).first()
        if not article:
            return jsonify({'error': '文章不存在'}), 404

        data = request.json
        if not data:
            return jsonify({'error': '请提供更新数据'}), 400

        # 更新字段
        if 'title' in data:
            article.title = data['title']
        if 'content' in data:
            article.content = data['content']
        if 'category' in data:
            article.category = data['category']
        if 'image_url' in data:
            article.image_url = data['image_url']
        if 'difficulty_level' in data:
            article.difficulty_level = data['difficulty_level']

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文章更新成功',
            'article': article.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"更新文章错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'更新文章失败: {str(e)}'}), 500

@health_articles_bp.route('/manage/articles/<article_id>', methods=['DELETE'])
def delete_article(article_id):
    """删除文章（仅限W_B02）"""
    # 检查权限
    has_permission, error_msg = check_worker_permission()
    if not has_permission:
        return jsonify({'error': error_msg}), 403

    try:
        HealthArticle, _, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        article = HealthArticle.query.filter_by(article_id=article_id).first()
        if not article:
            return jsonify({'error': '文章不存在'}), 404

        db.session.delete(article)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文章删除成功'
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"删除文章错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'删除文章失败: {str(e)}'}), 500

@health_articles_bp.route('/manage/articles', methods=['GET'])
def get_manage_articles():
    """获取文章管理列表（仅限W_B02）"""
    print("收到文章管理列表请求")

    # 检查权限
    has_permission, error_msg = check_worker_permission()
    if not has_permission:
        print(f"权限检查失败: {error_msg}")
        return jsonify({'error': error_msg}), 403

    try:
        HealthArticle, _, _ = get_models()
        if not HealthArticle:
            return jsonify({'error': '模型加载失败'}), 500

        # 获取查询参数
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))

        # 构建查询
        query = HealthArticle.query

        # 按分类筛选
        if category and category != 'all':
            query = query.filter(HealthArticle.category == category)

        # 搜索功能
        if search:
            query = query.filter(
                or_(
                    HealthArticle.title.contains(search),
                    HealthArticle.content.contains(search)
                )
            )

        # 按发布时间倒序排列
        query = query.order_by(HealthArticle.publish_time.desc())

        # 分页
        articles = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 转换为字典格式，包含管理所需的额外信息
        result = {
            'articles': [article.to_dict() for article in articles.items],
            'total': articles.total,
            'pages': articles.pages,
            'current_page': page,
            'per_page': per_page
        }

        return jsonify(result), 200

    except Exception as e:
        print(f"获取管理文章列表错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'获取文章列表失败: {str(e)}'}), 500

@health_articles_bp.route('/articles/recommended/<user_id>', methods=['GET'])
def get_recommended_articles(user_id):
    """根据用户健康状况推荐文章"""
    try:
        HealthArticle, ElderlyUser, _ = get_models()
        if not HealthArticle or not ElderlyUser:
            return jsonify({'error': '模型加载失败'}), 500

        # 获取用户信息
        elderly = ElderlyUser.query.filter_by(user_id=user_id).first()
        if not elderly:
            return jsonify({'error': '用户不存在'}), 404

        # 简单的推荐逻辑：根据年龄推荐相关文章
        recommended_categories = []
        if elderly.age and elderly.age >= 70:
            recommended_categories = ['养生', '疾病管理', '保健']
        else:
            recommended_categories = ['运动', '饮食', '心理']

        # 获取推荐文章
        articles = HealthArticle.query.filter(
            HealthArticle.category.in_(recommended_categories)
        ).order_by(HealthArticle.read_count.desc()).limit(5).all()

        result = [article.to_dict() for article in articles]

        return jsonify({'recommended': result}), 200

    except Exception as e:
        print(f"获取推荐文章错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'获取推荐文章失败: {str(e)}'}), 500
