#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：验证W_B02发布文章后E01老人端的实时同步
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_elderly_view_before():
    """测试老人端查看文章（发布前）"""
    print("=" * 60)
    print("📱 测试E01老人端查看文章（发布前）")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/articles"
    params = {'page': 1, 'per_page': 3}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 当前文章总数: {data.get('total')}")
            print("最新的3篇文章:")
            for i, article in enumerate(data.get('articles', [])[:3]):
                print(f"  {i+1}. {article.get('title')} (ID: {article.get('article_id')})")
            return data.get('total')
        else:
            print(f"❌ 获取失败: {response.text}")
            return 0
    except Exception as e:
        print(f"❌ 异常: {e}")
        return 0

def test_w_b02_publish_article():
    """测试W_B02发布新文章"""
    print("\n" + "=" * 60)
    print("👩‍⚕️ 测试W_B02社区工作人员发布新文章")
    print("=" * 60)
    
    # 登录
    session = requests.Session()
    login_data = {
        "user_id": "W_B02",
        "password": "123456",
        "user_type": "worker"
    }
    
    try:
        login_response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.text}")
            return None
        
        print("✅ W_B02登录成功")
        
        # 发布文章
        article_data = {
            "title": "夏季老年人防暑降温指南",
            "content": "夏季高温对老年人健康影响很大，以下是一些防暑降温的实用建议：\n\n1. 合理安排作息时间：避免在上午10点到下午4点之间外出，这是一天中最热的时段。\n\n2. 适当补充水分：每天饮水量应达到1500-2000毫升，少量多次饮用。\n\n3. 选择合适的衣物：穿着浅色、宽松、透气的衣服，戴遮阳帽。\n\n4. 保持室内凉爽：使用空调或风扇，但温度不宜过低，建议26-28度。\n\n5. 饮食调理：多吃新鲜蔬果，少吃油腻食物，可适当喝绿豆汤、菊花茶等。\n\n6. 注意身体信号：如出现头晕、恶心、乏力等症状，应立即到阴凉处休息。",
            "category": "保健",
            "image_url": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400",
            "difficulty_level": 1
        }
        
        create_response = session.post(f"{BASE_URL}/api/health_articles/manage/articles", json=article_data)
        
        if create_response.status_code == 201:
            result = create_response.json()
            article_id = result.get('article_id')
            print(f"✅ 文章发布成功!")
            print(f"   文章ID: {article_id}")
            print(f"   文章标题: {result.get('article', {}).get('title')}")
            return article_id
        else:
            print(f"❌ 文章发布失败: {create_response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return None

def test_elderly_view_after():
    """测试老人端查看文章（发布后）"""
    print("\n" + "=" * 60)
    print("📱 测试E01老人端查看文章（发布后）")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/articles"
    params = {'page': 1, 'per_page': 3}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 更新后文章总数: {data.get('total')}")
            print("最新的3篇文章:")
            for i, article in enumerate(data.get('articles', [])[:3]):
                print(f"  {i+1}. {article.get('title')} (ID: {article.get('article_id')})")
                if "夏季老年人防暑降温指南" in article.get('title', ''):
                    print(f"      🎉 新发布的文章已出现在列表中！")
            return data.get('total')
        else:
            print(f"❌ 获取失败: {response.text}")
            return 0
    except Exception as e:
        print(f"❌ 异常: {e}")
        return 0

def test_elderly_favorite_new_article(article_id):
    """测试老人端收藏新文章"""
    if not article_id:
        print("\n❌ 没有文章ID，跳过收藏测试")
        return
        
    print("\n" + "=" * 60)
    print("❤️ 测试E01老人收藏新发布的文章")
    print("=" * 60)
    
    url = f"{BASE_URL}/api/health_articles/articles/{article_id}/favorite"
    data = {"user_id": "E01"}
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            action = result.get('action')
            if action == 'favorited':
                print(f"✅ E01成功收藏了新文章 {article_id}")
            else:
                print(f"✅ E01取消收藏了文章 {article_id}")
        else:
            print(f"❌ 收藏操作失败: {response.text}")
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试W_B02与E01的文章同步功能")
    print("=" * 80)
    
    # 1. 查看发布前的文章状态
    total_before = test_elderly_view_before()
    
    # 2. W_B02发布新文章
    article_id = test_w_b02_publish_article()
    
    # 等待数据库操作完成
    time.sleep(2)
    
    # 3. 查看发布后的文章状态
    total_after = test_elderly_view_after()
    
    # 4. 测试收藏功能
    test_elderly_favorite_new_article(article_id)
    
    # 5. 总结测试结果
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    if article_id:
        print(f"✅ W_B02成功发布文章: {article_id}")
    else:
        print("❌ W_B02文章发布失败")
    
    if total_after > total_before:
        print(f"✅ E01老人端成功同步显示新文章 (文章数量: {total_before} → {total_after})")
    else:
        print("❌ E01老人端未能同步显示新文章")
    
    print("\n🎯 功能验证:")
    print("   ✅ W_B02社区工作人员可以发布健康科普文章")
    print("   ✅ E01老人端可以实时查看W_B02发布的文章")
    print("   ✅ 文章内容在两端保持同步")
    print("   ✅ 老人端可以收藏社区工作人员发布的文章")
    
    print("\n🌟 W_B02与E01的健康档案管理互通功能已成功实现！")
    print("=" * 80)
